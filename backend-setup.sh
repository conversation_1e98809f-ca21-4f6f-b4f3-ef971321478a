#!/usr/bin/env bash
set -euo pipefail

# Create directories
mkdir -p {backend,frontend,docs,scripts,.github/workflows}

# ---------------------
# Backend structure
# ---------------------
mkdir -p backend/{cmd/server,internal,tests/{integration,unit/{service,utils},fixtures},pkg/types}
mkdir -p backend/internal/{api/{handlers,middleware,routes,dto},auth,config,db/{migrations,queries,sqlc},service,storage,utils}

# ---------------------
# GitHub workflows
# ---------------------
mkdir -p .github/workflows

# ---------------------
# Touch placeholder files
# ---------------------

# Root files
touch {README.md,docker-compose.yml,docker-compose.prod.yml,.gitignore,.env.example}

# Backend files
touch backend/{Dockerfile,Dockerfile.prod,.env.example,.env.development,go.mod,go.sum,.air.toml,sqlc.yaml,.golangci.yml}

touch backend/internal/db/migrations/{001_create_users.{up,down}.sql,002_create_properties.{up,down}.sql,003_create_units.{up,down}.sql,004_create_tenants.{up,down}.sql,005_create_leases.{up,down}.sql,006_create_payments.{up,down}.sql,007_create_maintenance.{up,down}.sql,008_create_attachments.{up,down}.sql,009_create_refresh_tokens.{up,down}.sql}

# Docs
touch docs/{api.md,deployment.md,development.md,database-schema.md}

# Scripts
touch scripts/{migrate-up.sh,migrate-down.sh,seed-dev-data.sh,backup-db.sh}

# GitHub workflows
touch .github/workflows/{ci.yml,deploy-staging.yml,deploy-production.yml}

echo "✅ Backend structure created successfully!"
