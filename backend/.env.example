# Database Configuration
DATABASE_URL=postgres://postgres:password@localhost:5432/rental_mvp?sslmode=disable
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=rental_mvp
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_SSL_MODE=disable
DATABASE_MAX_CONNECTIONS=25
DATABASE_MAX_IDLE_CONNECTIONS=10
DATABASE_MAX_LIFETIME_MINUTES=5

# Server Configuration
PORT=8080
HOST=0.0.0.0
ENVIRONMENT=development
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
TRUSTED_PROXIES=127.0.0.1

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-at-least-32-chars
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=168h
REFRESH_TOKEN_COOKIE_NAME=refresh_token

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=debug
LOG_FORMAT=json

# File Storage (S3-compatible)
STORAGE_PROVIDER=local
AWS_REGION=us-east-1
AWS_BUCKET=rental-attachments
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_ENDPOINT=
LOCAL_STORAGE_PATH=./uploads

# Redis (optional - for caching/sessions)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
CACHE_TTL_MINUTES=60

# Migration Settings
MIGRATIONS_PATH=./migrations
AUTO_MIGRATE=true

# Security
BCRYPT_COST=12
CSRF_SECRET=another-32-char-secret-for-csrf-protection
SESSION_SECRET=yet-another-32-char-secret-for-sessions
