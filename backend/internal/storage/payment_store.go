package storage

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

type Payment struct {
	ID            string    `json:"id" db:"id"`
	LeaseID       string    `json:"lease_id" db:"lease_id"`
	Amount        float64   `json:"amount" db:"amount"`
	PaidAt        time.Time `json:"paid_at" db:"paid_at"`
	PaymentMethod *string   `json:"payment_method" db:"payment_method"`
	Notes         *string   `json:"notes" db:"notes"`
	CreatedBy     *string   `json:"created_by" db:"created_by"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`

	// Joined fields
	LeaseRentAmount *float64 `json:"lease_rent_amount,omitempty" db:"lease_rent_amount"`
	UnitName        *string  `json:"unit_name,omitempty" db:"unit_name"`
	TenantName      *string  `json:"tenant_name,omitempty" db:"tenant_name"`
}

type PaymentStore struct {
	db *pgxpool.Pool
}

func NewPaymentStore(db *pgxpool.Pool) *PaymentStore {
	return &PaymentStore{db: db}
}

func (r *PaymentStore) Create(ctx context.Context, leaseID string, amount float64, paidAt time.Time, paymentMethod, notes, createdBy *string) (*Payment, error) {
	query := `
        INSERT INTO payments (lease_id, amount, paid_at, payment_method, notes, created_by)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, lease_id, amount, paid_at, payment_method, notes, created_by, created_at`

	var payment Payment
	err := r.db.QueryRow(ctx, query, leaseID, amount, paidAt, paymentMethod, notes, createdBy).Scan(
		&payment.ID, &payment.LeaseID, &payment.Amount, &payment.PaidAt,
		&payment.PaymentMethod, &payment.Notes, &payment.CreatedBy, &payment.CreatedAt)
	if err != nil {
		return nil, err
	}

	return &payment, nil
}

func (r *PaymentStore) GetByOwner(ctx context.Context, ownerID string) ([]Payment, error) {
	query := `
        SELECT p.id, p.lease_id, p.amount, p.paid_at, p.payment_method, p.notes, p.created_by, p.created_at,
               l.rent_amount as lease_rent_amount,
               u.name as unit_name,
               t.name as tenant_name
        FROM payments p
        JOIN leases l ON p.lease_id = l.id
        JOIN units u ON l.unit_id = u.id
        LEFT JOIN tenants t ON l.tenant_id = t.id
        JOIN properties prop ON u.property_id = prop.id
        WHERE prop.owner_id = $1
        ORDER BY p.paid_at DESC`

	rows, err := r.db.Query(ctx, query, ownerID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var payments []Payment
	for rows.Next() {
		var payment Payment
		err := rows.Scan(
			&payment.ID, &payment.LeaseID, &payment.Amount, &payment.PaidAt,
			&payment.PaymentMethod, &payment.Notes, &payment.CreatedBy, &payment.CreatedAt,
			&payment.LeaseRentAmount, &payment.UnitName, &payment.TenantName)
		if err != nil {
			return nil, err
		}
		payments = append(payments, payment)
	}

	return payments, nil
}

func (r *PaymentStore) GetByLeaseID(ctx context.Context, leaseID string) ([]Payment, error) {
	query := `
        SELECT id, lease_id, amount, paid_at, payment_method, notes, created_by, created_at
        FROM payments
        WHERE lease_id = $1
        ORDER BY paid_at DESC`

	rows, err := r.db.Query(ctx, query, leaseID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var payments []Payment
	for rows.Next() {
		var payment Payment
		err := rows.Scan(
			&payment.ID, &payment.LeaseID, &payment.Amount, &payment.PaidAt,
			&payment.PaymentMethod, &payment.Notes, &payment.CreatedBy, &payment.CreatedAt)
		if err != nil {
			return nil, err
		}
		payments = append(payments, payment)
	}

	return payments, nil
}

func (r *PaymentStore) GetRecent(ctx context.Context, ownerID string, limit int) ([]Payment, error) {
	query := `
        SELECT p.id, p.lease_id, p.amount, p.paid_at, p.payment_method, p.notes, p.created_by, p.created_at,
               l.rent_amount as lease_rent_amount,
               u.name as unit_name,
               t.name as tenant_name
        FROM payments p
        JOIN leases l ON p.lease_id = l.id
        JOIN units u ON l.unit_id = u.id
        LEFT JOIN tenants t ON l.tenant_id = t.id
        JOIN properties prop ON u.property_id = prop.id
        WHERE prop.owner_id = $1
        ORDER BY p.paid_at DESC
        LIMIT $2`

	rows, err := r.db.Query(ctx, query, ownerID, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var payments []Payment
	for rows.Next() {
		var payment Payment
		err := rows.Scan(
			&payment.ID, &payment.LeaseID, &payment.Amount, &payment.PaidAt,
			&payment.PaymentMethod, &payment.Notes, &payment.CreatedBy, &payment.CreatedAt,
			&payment.LeaseRentAmount, &payment.UnitName, &payment.TenantName)
		if err != nil {
			return nil, err
		}
		payments = append(payments, payment)
	}

	return payments, nil
}
