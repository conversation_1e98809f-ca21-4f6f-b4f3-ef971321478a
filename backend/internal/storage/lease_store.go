package storage

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

type Tenant struct {
	ID        string    `json:"id" db:"id"`
	Name      string    `json:"name" db:"name"`
	Email     *string   `json:"email" db:"email"`
	Phone     *string   `json:"phone" db:"phone"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

type Lease struct {
	ID            string     `json:"id" db:"id"`
	UnitID        string     `json:"unit_id" db:"unit_id"`
	TenantID      *string    `json:"tenant_id" db:"tenant_id"`
	StartDate     time.Time  `json:"start_date" db:"start_date"`
	EndDate       *time.Time `json:"end_date" db:"end_date"`
	RentAmount    float64    `json:"rent_amount" db:"rent_amount"`
	DepositAmount float64    `json:"deposit_amount" db:"deposit_amount"`
	Active        bool       `json:"active" db:"active"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`

	// Joined fields
	UnitName    *string `json:"unit_name,omitempty" db:"unit_name"`
	PropertyID  *string `json:"property_id,omitempty" db:"property_id"`
	TenantName  *string `json:"tenant_name,omitempty" db:"tenant_name"`
	TenantEmail *string `json:"tenant_email,omitempty" db:"tenant_email"`
}

type LeaseStore struct {
	db *pgxpool.Pool
}

func NewLeaseStore(db *pgxpool.Pool) *LeaseStore {
	return &LeaseStore{db: db}
}

// Tenants
func (r *LeaseStore) CreateTenant(ctx context.Context, name string, email, phone *string) (*Tenant, error) {
	query := `
        INSERT INTO tenants (name, email, phone)
        VALUES ($1, $2, $3)
        RETURNING id, name, email, phone, created_at, updated_at`

	var tenant Tenant
	err := r.db.QueryRow(ctx, query, name, email, phone).Scan(
		&tenant.ID, &tenant.Name, &tenant.Email, &tenant.Phone,
		&tenant.CreatedAt, &tenant.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &tenant, nil
}

func (r *LeaseStore) GetTenants(ctx context.Context) ([]Tenant, error) {
	query := `
        SELECT id, name, email, phone, created_at, updated_at
        FROM tenants
        ORDER BY name`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tenants []Tenant
	for rows.Next() {
		var tenant Tenant
		err := rows.Scan(
			&tenant.ID, &tenant.Name, &tenant.Email, &tenant.Phone,
			&tenant.CreatedAt, &tenant.UpdatedAt)
		if err != nil {
			return nil, err
		}
		tenants = append(tenants, tenant)
	}

	return tenants, nil
}

func (r *LeaseStore) GetTenantByID(ctx context.Context, tenantID string) (*Tenant, error) {
	query := `
        SELECT id, name, email, phone, created_at, updated_at
        FROM tenants
        WHERE id = $1`

	var tenant Tenant
	err := r.db.QueryRow(ctx, query, tenantID).Scan(
		&tenant.ID, &tenant.Name, &tenant.Email, &tenant.Phone,
		&tenant.CreatedAt, &tenant.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &tenant, nil
}

// Leases
func (r *LeaseStore) CreateLease(ctx context.Context, unitID string, tenantID *string, startDate time.Time, endDate *time.Time, rentAmount, depositAmount float64) (*Lease, error) {
	query := `
        INSERT INTO leases (unit_id, tenant_id, start_date, end_date, rent_amount, deposit_amount, active)
        VALUES ($1, $2, $3, $4, $5, $6, true)
        RETURNING id, unit_id, tenant_id, start_date, end_date, rent_amount, deposit_amount, active, created_at, updated_at`

	var lease Lease
	err := r.db.QueryRow(ctx, query, unitID, tenantID, startDate, endDate, rentAmount, depositAmount).Scan(
		&lease.ID, &lease.UnitID, &lease.TenantID, &lease.StartDate, &lease.EndDate,
		&lease.RentAmount, &lease.DepositAmount, &lease.Active,
		&lease.CreatedAt, &lease.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &lease, nil
}

func (r *LeaseStore) GetLeasesByOwner(ctx context.Context, ownerID string, activeOnly bool) ([]Lease, error) {
	baseQuery := `
        SELECT l.id, l.unit_id, l.tenant_id, l.start_date, l.end_date, l.rent_amount, l.deposit_amount, l.active,
               l.created_at, l.updated_at,
               u.name as unit_name, u.property_id,
               t.name as tenant_name, t.email as tenant_email
        FROM leases l
        JOIN units u ON l.unit_id = u.id
        LEFT JOIN tenants t ON l.tenant_id = t.id
        JOIN properties p ON u.property_id = p.id
        WHERE p.owner_id = $1`

	var query string
	var args []interface{}

	if activeOnly {
		query = baseQuery + " AND l.active = true ORDER BY l.created_at DESC"
		args = []interface{}{ownerID}
	} else {
		query = baseQuery + " ORDER BY l.created_at DESC"
		args = []interface{}{ownerID}
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var leases []Lease
	for rows.Next() {
		var lease Lease
		err := rows.Scan(
			&lease.ID, &lease.UnitID, &lease.TenantID, &lease.StartDate, &lease.EndDate,
			&lease.RentAmount, &lease.DepositAmount, &lease.Active,
			&lease.CreatedAt, &lease.UpdatedAt,
			&lease.UnitName, &lease.PropertyID, &lease.TenantName, &lease.TenantEmail)
		if err != nil {
			return nil, err
		}
		leases = append(leases, lease)
	}

	return leases, nil
}

func (r *LeaseStore) GetLeaseByID(ctx context.Context, leaseID string) (*Lease, error) {
	query := `
        SELECT l.id, l.unit_id, l.tenant_id, l.start_date, l.end_date, l.rent_amount, l.deposit_amount, l.active,
               l.created_at, l.updated_at,
               u.name as unit_name, u.property_id,
               t.name as tenant_name, t.email as tenant_email
        FROM leases l
        JOIN units u ON l.unit_id = u.id
        LEFT JOIN tenants t ON l.tenant_id = t.id
        WHERE l.id = $1`

	var lease Lease
	err := r.db.QueryRow(ctx, query, leaseID).Scan(
		&lease.ID, &lease.UnitID, &lease.TenantID, &lease.StartDate, &lease.EndDate,
		&lease.RentAmount, &lease.DepositAmount, &lease.Active,
		&lease.CreatedAt, &lease.UpdatedAt,
		&lease.UnitName, &lease.PropertyID, &lease.TenantName, &lease.TenantEmail)
	if err != nil {
		return nil, err
	}

	return &lease, nil
}

func (r *LeaseStore) DeactivateLease(ctx context.Context, leaseID string) error {
	query := `UPDATE leases SET active = false, updated_at = now() WHERE id = $1`
	_, err := r.db.Exec(ctx, query, leaseID)
	return err
}
