package storage

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type Property struct {
	ID          string    `json:"id" db:"id"`
	OwnerID     string    `json:"owner_id" db:"owner_id"`
	Title       string    `json:"title" db:"title"`
	Address     string    `json:"address" db:"address"`
	City        *string   `json:"city" db:"city"`
	Country     *string   `json:"country" db:"country"`
	Description *string   `json:"description" db:"description"`
	UnitsCount  int       `json:"units_count" db:"units_count"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

type Unit struct {
	ID         string    `json:"id" db:"id"`
	PropertyID string    `json:"property_id" db:"property_id"`
	Name       string    `json:"name" db:"name"`
	RentAmount float64   `json:"rent_amount" db:"rent_amount"`
	Status     string    `json:"status" db:"status"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
}

type PropertyStore struct {
	db *pgxpool.Pool
}

func NewPropertyStore(db *pgxpool.Pool) *PropertyStore {
	return &PropertyStore{db: db}
}

// Properties
func (r *PropertyStore) Create(ctx context.Context, ownerID, title, address string, city, country, description *string) (*Property, error) {
	query := `
        INSERT INTO properties (owner_id, title, address, city, country, description)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, owner_id, title, address, city, country, description, created_at, updated_at`

	var property Property
	err := r.db.QueryRow(ctx, query, ownerID, title, address, city, country, description).Scan(
		&property.ID, &property.OwnerID, &property.Title, &property.Address,
		&property.City, &property.Country, &property.Description,
		&property.CreatedAt, &property.UpdatedAt)
	if err != nil {
		return nil, err
	}

	property.UnitsCount = 0 // New property has no units
	return &property, nil
}

func (r *PropertyStore) GetByOwner(ctx context.Context, ownerID string) ([]Property, error) {
	query := `
        SELECT p.id, p.owner_id, p.title, p.address, p.city, p.country, p.description,
               p.created_at, p.updated_at, COUNT(u.id)::int as units_count
        FROM properties p
        LEFT JOIN units u ON p.id = u.property_id
        WHERE p.owner_id = $1
        GROUP BY p.id, p.owner_id, p.title, p.address, p.city, p.country, p.description, p.created_at, p.updated_at
        ORDER BY p.created_at DESC`

	rows, err := r.db.Query(ctx, query, ownerID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var properties []Property
	for rows.Next() {
		var property Property
		err := rows.Scan(
			&property.ID, &property.OwnerID, &property.Title, &property.Address,
			&property.City, &property.Country, &property.Description,
			&property.CreatedAt, &property.UpdatedAt, &property.UnitsCount)
		if err != nil {
			return nil, err
		}
		properties = append(properties, property)
	}

	return properties, nil
}

func (r *PropertyStore) GetByID(ctx context.Context, propertyID, ownerID string) (*Property, error) {
	query := `
        SELECT p.id, p.owner_id, p.title, p.address, p.city, p.country, p.description,
               p.created_at, p.updated_at, COUNT(u.id)::int as units_count
        FROM properties p
        LEFT JOIN units u ON p.id = u.property_id
        WHERE p.id = $1 AND p.owner_id = $2
        GROUP BY p.id, p.owner_id, p.title, p.address, p.city, p.country, p.description, p.created_at, p.updated_at`

	var property Property
	err := r.db.QueryRow(ctx, query, propertyID, ownerID).Scan(
		&property.ID, &property.OwnerID, &property.Title, &property.Address,
		&property.City, &property.Country, &property.Description,
		&property.CreatedAt, &property.UpdatedAt, &property.UnitsCount)
	if err != nil {
		return nil, err
	}

	return &property, nil
}

func (r *PropertyStore) Update(ctx context.Context, propertyID, ownerID, title, address string, city, country, description *string) (*Property, error) {
	query := `
        UPDATE properties
        SET title = $3, address = $4, city = $5, country = $6, description = $7, updated_at = now()
        WHERE id = $1 AND owner_id = $2
        RETURNING id, owner_id, title, address, city, country, description, created_at, updated_at`

	var property Property
	err := r.db.QueryRow(ctx, query, propertyID, ownerID, title, address, city, country, description).Scan(
		&property.ID, &property.OwnerID, &property.Title, &property.Address,
		&property.City, &property.Country, &property.Description,
		&property.CreatedAt, &property.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &property, nil
}

func (r *PropertyStore) Delete(ctx context.Context, propertyID, ownerID string) error {
	query := `DELETE FROM properties WHERE id = $1 AND owner_id = $2`
	result, err := r.db.Exec(ctx, query, propertyID, ownerID)
	if err != nil {
		return err
	}

	if result.RowsAffected() == 0 {
		return pgx.ErrNoRows
	}

	return nil
}

// Units
func (r *PropertyStore) CreateUnit(ctx context.Context, propertyID, name string, rentAmount float64, status string) (*Unit, error) {
	if status == "" {
		status = "vacant"
	}

	query := `
        INSERT INTO units (property_id, name, rent_amount, status)
        VALUES ($1, $2, $3, $4)
        RETURNING id, property_id, name, rent_amount, status, created_at, updated_at`

	var unit Unit
	err := r.db.QueryRow(ctx, query, propertyID, name, rentAmount, status).Scan(
		&unit.ID, &unit.PropertyID, &unit.Name, &unit.RentAmount,
		&unit.Status, &unit.CreatedAt, &unit.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &unit, nil
}

func (r *PropertyStore) GetUnitsByProperty(ctx context.Context, propertyID string) ([]Unit, error) {
	query := `
        SELECT id, property_id, name, rent_amount, status, created_at, updated_at
        FROM units
        WHERE property_id = $1
        ORDER BY name`

	rows, err := r.db.Query(ctx, query, propertyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var units []Unit
	for rows.Next() {
		var unit Unit
		err := rows.Scan(
			&unit.ID, &unit.PropertyID, &unit.Name, &unit.RentAmount,
			&unit.Status, &unit.CreatedAt, &unit.UpdatedAt)
		if err != nil {
			return nil, err
		}
		units = append(units, unit)
	}

	return units, nil
}

func (r *PropertyStore) GetUnitByID(ctx context.Context, unitID string) (*Unit, error) {
	query := `
        SELECT id, property_id, name, rent_amount, status, created_at, updated_at
        FROM units
        WHERE id = $1`

	var unit Unit
	err := r.db.QueryRow(ctx, query, unitID).Scan(
		&unit.ID, &unit.PropertyID, &unit.Name, &unit.RentAmount,
		&unit.Status, &unit.CreatedAt, &unit.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &unit, nil
}

func (r *PropertyStore) UpdateUnit(ctx context.Context, unitID, name string, rentAmount float64, status string) (*Unit, error) {
	query := `
        UPDATE units
        SET name = $2, rent_amount = $3, status = $4, updated_at = now()
        WHERE id = $1
        RETURNING id, property_id, name, rent_amount, status, created_at, updated_at`

	var unit Unit
	err := r.db.QueryRow(ctx, query, unitID, name, rentAmount, status).Scan(
		&unit.ID, &unit.PropertyID, &unit.Name, &unit.RentAmount,
		&unit.Status, &unit.CreatedAt, &unit.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return &unit, nil
}

func (r *PropertyStore) DeleteUnit(ctx context.Context, unitID string) error {
	query := `DELETE FROM units WHERE id = $1`
	_, err := r.db.Exec(ctx, query, unitID)
	return err
}
