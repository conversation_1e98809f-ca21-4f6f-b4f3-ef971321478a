package storage

import (
    "context"
    "time"

    "github.com/jackc/pgx/v5/pgxpool"
)

type DashboardStats struct {
    TotalProperties   int     `json:"total_properties" db:"total_properties"`
    TotalUnits        int     `json:"total_units" db:"total_units"`
    OccupiedUnits     int     `json:"occupied_units" db:"occupied_units"`
    VacantUnits       int     `json:"vacant_units" db:"vacant_units"`
    ActiveLeases      int     `json:"active_leases" db:"total_active_leases"`
    TotalMonthlyRent  float64 `json:"total_monthly_rent" db:"total_monthly_rent"`
    PendingMaintenance int    `json:"pending_maintenance" db:"pending_maintenance"`
    OverduePayments   int     `json:"overdue_payments" db:"overdue_payments"`
}

type UnitOccupancy struct {
    UnitID          string     `json:"unit_id" db:"unit_id"`
    PropertyID      string     `json:"property_id" db:"property_id"`
    UnitName        string     `json:"unit_name" db:"unit_name"`
    RentAmount      float64    `json:"rent_amount" db:"rent_amount"`
    Status          string     `json:"status" db:"status"`
    CurrentLeaseID  *string    `json:"current_lease_id" db:"current_lease_id"`
    TenantID        *string    `json:"tenant_id" db:"tenant_id"`
    TenantName      *string    `json:"tenant_name" db:"tenant_name"`
    TenantEmail     *string    `json:"tenant_email" db:"tenant_email"`
    StartDate       *time.Time `json:"start_date" db:"start_date"`
    EndDate         *time.Time `json:"end_date" db:"end_date"`
    OccupancyStatus string     `json:"occupancy_status" db:"occupancy_status"`
}

type RecentActivity struct {
    ActivityType string    `json:"activity_type" db:"activity_type"`
    ActivityID   string    `json:"activity_id" db:"activity_id"`
    Description  string    `json:"description" db:"description"`
    ActivityDate time.Time `json:"activity_date" db:"activity_date"`
}

type OverdueLease struct {
	LeaseID     string  `json:"lease_id"`
	UnitName    string  `json:"unit_name"`
	TenantName  *string `json:"tenant_name"`
	RentDue     float64 `json:"rent_due"`
	DaysPastDue int     `json:"days_past_due"`
}

type DashboardStore struct {
    db *pgxpool.Pool
}

func NewDashboardStore(db *pgxpool.Pool) *DashboardStore {
    return &DashboardStore{db: db}
}

// Using the SQL function from migrations
func (r *DashboardStore) GetStats(ctx context.Context, ownerID string) (*DashboardStats, error) {
    query := `SELECT * FROM get_property_dashboard_stats($1)`

    var stats DashboardStats
    err := r.db.QueryRow(ctx, query, ownerID).Scan(
        &stats.TotalProperties, &stats.TotalUnits, &stats.OccupiedUnits,
        &stats.VacantUnits, &stats.ActiveLeases, &stats.TotalMonthlyRent,
        &stats.PendingMaintenance, &stats.OverduePayments)
    if err != nil {
        return nil, err
    }

    return &stats, nil
}

// Using the unit_occupancy view from migrations
func (r *DashboardStore) GetUnitOccupancy(ctx context.Context, ownerID string) ([]UnitOccupancy, error) {
    query := `
        SELECT uo.unit_id, uo.property_id, uo.unit_name, uo.rent_amount, uo.status,
               uo.current_lease_id, uo.tenant_id, uo.tenant_name, uo.tenant_email,
               uo.start_date, uo.end_date, uo.occupancy_status
        FROM unit_occupancy uo
        JOIN properties p ON uo.property_id = p.id
        WHERE p.owner_id = $1
        ORDER BY uo.unit_name`

    rows, err := r.db.Query(ctx, query, ownerID)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    var occupancies []UnitOccupancy
    for rows.Next() {
        var occupancy UnitOccupancy
        err := rows.Scan(
            &occupancy.UnitID, &occupancy.PropertyID, &occupancy.UnitName,
            &occupancy.RentAmount, &occupancy.Status, &occupancy.CurrentLeaseID,
            &occupancy.TenantID, &occupancy.TenantName, &occupancy.TenantEmail,
            &occupancy.StartDate, &occupancy.EndDate, &occupancy.OccupancyStatus)
        if err != nil {
            return nil, err
        }
        occupancies = append(occupancies, occupancy)
    }

    return occupancies, nil
}

// Using the recent activity query from migrations
func (r *DashboardStore) GetRecentActivity(ctx context.Context, ownerID string, limit int) ([]RecentActivity, error) {
    // This uses the complex query from our migrations
    query := `
        SELECT 
            'payment' as activity_type,
            p.id::text as activity_id,
            'Payment of $' || p.amount || ' received for ' || u.name as description,
            p.created_at as activity_date
        FROM payments p
        JOIN leases l ON p.lease_id = l.id
        JOIN units u ON l.unit_id = u.id
        JOIN properties prop ON u.property_id = prop.id
        WHERE prop.owner_id = $1

        UNION ALL

        SELECT 
            'maintenance' as activity_type,
            mr.id::text as activity_id,
            'Maintenance request: ' || mr.title || ' for ' || u.name as description,
            mr.created_at as activity_date
        FROM maintenance_requests mr
        JOIN units u ON mr.unit_id = u.id
        JOIN properties p ON u.property_id = p.id
        WHERE p.owner_id = $1

        UNION ALL

        SELECT 
            'lease' as activity_type,
            l.id::text as activity_id,
            'New lease created for ' || u.name || COALESCE(' with ' || t.name, '') as description,
            l.created_at as activity_date
        FROM leases l
        JOIN units u ON l.unit_id = u.id
        LEFT JOIN tenants t ON l.tenant_id = t.id
        JOIN properties p ON u.property_id = p.id
        WHERE p.owner_id = $1

        ORDER BY activity_date DESC
        LIMIT $2`

    rows, err := r.db.Query(ctx, query, ownerID, limit)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    var activities []RecentActivity
    for rows.Next() {
        var activity RecentActivity
        err := rows.Scan(
            &activity.ActivityType, &activity.ActivityID,
            &activity.Description, &activity.ActivityDate)
        if err != nil {
            return nil, err
        }
        activities = append(activities, activity)
    }

    return activities, nil
}

// Using the calculate_rent_due function from migrations
func (r *DashboardStore) GetOverdueLeases(ctx context.Context, ownerID string) ([]OverdueLease, error) {
    query := `
        SELECT l.id, u.name, t.name, calculate_rent_due(l.id) as rent_due,
               (CURRENT_DATE - l.start_date)::int as days_past_due
        FROM leases l
        JOIN units u ON l.unit_id = u.id
        LEFT JOIN tenants t ON l.tenant_id = t.id
        JOIN properties p ON u.property_id = p.id
        WHERE p.owner_id = $1 
          AND l.active = true 
          AND calculate_rent_due(l.id) > 0
        ORDER BY rent_due DESC`

    rows, err := r.db.Query(ctx, query, ownerID)
    if err != nil {
        return nil, err
    }
    defer rows.Close()

    var overdueLeases []OverdueLease

    for rows.Next() {
        var lease OverdueLease
        err := rows.Scan(&lease.LeaseID, &lease.UnitName, &lease.TenantName, &lease.RentDue, &lease.DaysPastDue)
        if err != nil {
            return nil, err
        }
        overdueLeases = append(overdueLeases, lease)
    }

    return overdueLeases, nil
}