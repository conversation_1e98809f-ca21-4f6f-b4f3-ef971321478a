package dto

import (
	"time"

	"github.com/nyunja/rentbase/backend/internal/storage"
	"github.com/nyunja/rentbase/backend/internal/utils"
)

type CreateTenantRequest struct {
	Name  string  `json:"name"`
	Email *string `json:"email,omitempty"`
	Phone *string `json:"phone,omitempty"`
}

func (r *CreateTenantRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateRequired(r.Name, "name"); err.Message != "" {
		errors = append(errors, err)
	}

	if r.Email != nil && *r.Email != "" {
		if err := utils.ValidateEmail(*r.Email); err.Message != "" {
			errors = append(errors, err)
		}
	}

	return errors
}

type TenantResponse struct {
	ID        string  `json:"id"`
	Name      string  `json:"name"`
	Email     *string `json:"email"`
	Phone     *string `json:"phone"`
	CreatedAt string  `json:"created_at"`
	UpdatedAt string  `json:"updated_at"`
}

func TenantToResponse(tenant *storage.Tenant) TenantResponse {
	return TenantResponse{
		ID:        tenant.ID,
		Name:      tenant.Name,
		Email:     tenant.Email,
		Phone:     tenant.Phone,
		CreatedAt: tenant.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt: tenant.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}
}

func TenantsToResponse(tenants []storage.Tenant) []TenantResponse {
	var responses []TenantResponse
	for _, tenant := range tenants {
		responses = append(responses, TenantToResponse(&tenant))
	}
	return responses
}

type CreateLeaseRequest struct {
	UnitID        string     `json:"unit_id"`
	TenantID      *string    `json:"tenant_id,omitempty"`
	StartDate     string     `json:"start_date"`
	EndDate       *string    `json:"end_date,omitempty"`
	RentAmount    float64    `json:"rent_amount"`
	DepositAmount float64    `json:"deposit_amount"`
}

func (r *CreateLeaseRequest) Validate() utils.ValidationErrors {
	var errors utils.ValidationErrors

	if err := utils.ValidateRequired(r.UnitID, "unit_id"); err.Message != "" {
		errors = append(errors, err)
	}

	if err := utils.ValidateRequired(r.StartDate, "start_date"); err.Message != "" {
		errors = append(errors, err)
	}

	if r.RentAmount < 0 {
		errors = append(errors, utils.ValidationError{
			Field:   "rent_amount",
			Message: "Rent amount must be non-negative",
		})
	}

	if r.DepositAmount < 0 {
		errors = append(errors, utils.ValidationError{
			Field:   "deposit_amount",
			Message: "Deposit amount must be non-negative",
		})
	}

	// Validate date format
	if _, err := time.Parse("2006-01-02", r.StartDate); err != nil {
		errors = append(errors, utils.ValidationError{
			Field:   "start_date",
			Message: "Start date must be in YYYY-MM-DD format",
		})
	}

	if r.EndDate != nil && *r.EndDate != "" {
		if _, err := time.Parse("2006-01-02", *r.EndDate); err != nil {
			errors = append(errors, utils.ValidationError{
				Field:   "end_date",
				Message: "End date must be in YYYY-MM-DD format",
			})
		}
	}

	return errors
}

type LeaseResponse struct {
	ID            string  `json:"id"`
	UnitID        string  `json:"unit_id"`
	UnitName      *string `json:"unit_name,omitempty"`
	PropertyID    *string `json:"property_id,omitempty"`
	TenantID      *string `json:"tenant_id"`
	TenantName    *string `json:"tenant_name,omitempty"`
	TenantEmail   *string `json:"tenant_email,omitempty"`
	StartDate     string  `json:"start_date"`
	EndDate       *string `json:"end_date"`
	RentAmount    float64 `json:"rent_amount"`
	DepositAmount float64 `json:"deposit_amount"`
	Active        bool    `json:"active"`
	CreatedAt     string  `json:"created_at"`
	UpdatedAt     string  `json:"updated_at"`
}

func LeaseToResponse(lease *storage.Lease) LeaseResponse {
	response := LeaseResponse{
		ID:            lease.ID,
		UnitID:        lease.UnitID,
		UnitName:      lease.UnitName,
		PropertyID:    lease.PropertyID,
		TenantID:      lease.TenantID,
		TenantName:    lease.TenantName,
		TenantEmail:   lease.TenantEmail,
		StartDate:     lease.StartDate.Format("2006-01-02"),
		RentAmount:    lease.RentAmount,
		DepositAmount: lease.DepositAmount,
		Active:        lease.Active,
		CreatedAt:     lease.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:     lease.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	if lease.EndDate != nil {
		endDate := lease.EndDate.Format("2006-01-02")
		response.EndDate = &endDate
	}

	return response
}

func LeasesToResponse(leases []storage.Lease) []LeaseResponse {
	var responses []LeaseResponse
	for _, lease := range leases {
		responses = append(responses, LeaseToResponse(&lease))
	}
	return responses
}