package dto

import "github.com/nyunja/rentbase/backend/internal/utils"

type SignupRequest struct {
    Email    string `json:"email"`
    Password string `json:"password"`
    Name     string `json:"name"`
}

func (s *SignupRequest) Validate() utils.ValidationErrors {
    var errors utils.ValidationErrors

    if err := utils.ValidateEmail(s.Email); err.Message != "" {
        errors = append(errors, err)
    }

    if err := utils.ValidatePassword(s.Password); err.Message != "" {
        errors = append(errors, err)
    }

    if err := utils.ValidateRequired(s.Name, "name"); err.Message != "" {
        errors = append(errors, err)
    }

    return errors
}

type LoginRequest struct {
    Email    string `json:"email"`
    Password string `json:"password"`
}

func (l *LoginRequest) Validate() utils.ValidationErrors {
    var errors utils.ValidationErrors

    if err := utils.ValidateEmail(l.<PERSON>ail); err.Message != "" {
        errors = append(errors, err)
    }

    if err := utils.ValidateRequired(l.Password, "password"); err.Message != "" {
        errors = append(errors, err)
    }

    return errors
}

type AuthResponse struct {
    AccessToken string    `json:"access_token"`
    TokenType   string    `json:"token_type"`
    ExpiresIn   int       `json:"expires_in"`
    User        UserInfo  `json:"user"`
}

type UserInfo struct {
    ID    string `json:"id"`
    Email string `json:"email"`
    Name  string `json:"name"`
}
