package middleware

import (
    "net/http"
    "sync"
    "time"

    "github.com/nyunja/rentbase/backend/internal/utils"
)

type RateLimiter struct {
    visitors map[string]*visitor
    mu       sync.RWMutex
    rate     int           // requests per minute
    burst    int           // burst capacity
    cleanup  time.Duration // cleanup interval
}

type visitor struct {
    tokens    int
    lastSeen  time.Time
    lastReset time.Time
}

func NewRateLimiter(requestsPerMinute, burst int) *RateLimiter {
    rl := &RateLimiter{
        visitors: make(map[string]*visitor),
        rate:     requestsPerMinute,
        burst:    burst,
        cleanup:  time.Minute,
    }

    // Start cleanup goroutine
    go rl.cleanupVisitors()

    return rl
}

func (rl *RateLimiter) RateLimit(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        ip := utils.GetClientIP(r)
        
        if !rl.allow(ip) {
            utils.WriteErrorResponse(w, http.StatusTooManyRequests, "Rate limit exceeded")
            return
        }

        next.ServeHTTP(w, r)
    })
}

func (rl *RateLimiter) allow(ip string) bool {
    rl.mu.Lock()
    defer rl.mu.Unlock()

    now := time.Now()
    v, exists := rl.visitors[ip]

    if !exists {
        rl.visitors[ip] = &visitor{
            tokens:    rl.burst - 1,
            lastSeen:  now,
            lastReset: now,
        }
        return true
    }

    // Update last seen
    v.lastSeen = now

    // Refill tokens based on time passed
    timePassed := now.Sub(v.lastReset)
    tokensToAdd := int(timePassed.Minutes()) * rl.rate
    
    if tokensToAdd > 0 {
        v.tokens = min(rl.burst, v.tokens+tokensToAdd)
        v.lastReset = now
    }

    if v.tokens > 0 {
        v.tokens--
        return true
    }

    return false
}

func (rl *RateLimiter) cleanupVisitors() {
    for {
        time.Sleep(rl.cleanup)

        rl.mu.Lock()
        for ip, v := range rl.visitors {
            if time.Since(v.lastSeen) > 3*time.Minute {
                delete(rl.visitors, ip)
            }
        }
        rl.mu.Unlock()
    }
}

func min(a, b int) int {
    if a < b {
        return a
    }
    return b
}