package handlers

import (
	"net/http"

	"github.com/google/uuid"
	"github.com/nyunja/rentbase/backend/internal/api/middleware"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/utils"
	"go.uber.org/zap"
)

type DashboardHandler struct {
	dashboardService *service.DashboardService
	logger           *zap.Logger
}

func NewDashboardHandler(dashboardService *service.DashboardService, logger *zap.Logger) *DashboardHandler {
	return &DashboardHandler{
		dashboardService: dashboardService,
		logger:           logger,
	}
}

func (h *DashboardHandler) GetSummary(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	_, err := uuid.Parse(userID)
	if err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid user ID format")
		return
	}

	summary, err := h.dashboardService.GetDashboardSummary(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get dashboard summary", zap.Error(err), zap.String("user_id", userID))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get dashboard summary")
		return
	}

	utils.WriteSuccessResponse(w, summary, "Dashboard summary retrieved successfully")
}
