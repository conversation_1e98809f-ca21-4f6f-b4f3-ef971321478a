package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/nyunja/rentbase/backend/internal/api/dto"
	"github.com/nyunja/rentbase/backend/internal/api/middleware"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/utils"
	"go.uber.org/zap"
)

type PropertyHandler struct {
	propertyService *service.PropertyService
	logger          *zap.Logger
}

func NewPropertyHandler(propertyService *service.PropertyService, logger *zap.Logger) *PropertyHandler {
	return &PropertyHandler{
		propertyService: propertyService,
		logger:          logger,
	}
}

func (h *PropertyHandler) GetProperties(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	properties, err := h.propertyService.GetPropertiesByOwner(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get properties", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve properties")
		return
	}

	response := dto.PropertiesToResponse(properties)
	utils.WriteSuccessResponse(w, response, "Properties retrieved successfully")
}

func (h *PropertyHandler) CreateProperty(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	var req dto.CreatePropertyRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	property, err := h.propertyService.CreateProperty(r.Context(), userID, req.Title, req.Address, req.City, req.Country, req.Description)
	if err != nil {
		h.logger.Error("Failed to create property", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create property")
		return
	}

	response := dto.PropertyToResponse(property)
	utils.WriteSuccessResponse(w, response, "Property created successfully")
}

func (h *PropertyHandler) GetProperty(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	propertyID := chi.URLParam(r, "id")
	if propertyID == "" {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Property ID is required")
		return
	}

	property, err := h.propertyService.GetPropertyByID(r.Context(), propertyID, userID)
	if err != nil {
		if err.Error() == "property not found" {
			utils.WriteErrorResponse(w, http.StatusNotFound, "Property not found")
			return
		}
		h.logger.Error("Failed to get property", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to retrieve property")
		return
	}

	response := dto.PropertyToResponse(property)
	utils.WriteSuccessResponse(w, response, "Property retrieved successfully")
}

func (h *PropertyHandler) CreateUnit(w http.ResponseWriter, r *http.Request) {
	userID := middleware.GetUserIDFromContext(r.Context())
	if userID == "" {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "User not authenticated")
		return
	}

	propertyID := chi.URLParam(r, "id")
	if propertyID == "" {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Property ID is required")
		return
	}

	// Verify property belongs to user
	_, err := h.propertyService.GetPropertyByID(r.Context(), propertyID, userID)
	if err != nil {
		if err.Error() == "property not found" {
			utils.WriteErrorResponse(w, http.StatusNotFound, "Property not found")
			return
		}
		h.logger.Error("Failed to verify property ownership", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to verify property")
		return
	}

	var req dto.CreateUnitRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	unit, err := h.propertyService.CreateUnit(r.Context(), propertyID, req.Name, req.RentAmount, req.Status)
	if err != nil {
		h.logger.Error("Failed to create unit", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create unit")
		return
	}

	response := dto.UnitToResponse(unit)
	utils.WriteSuccessResponse(w, response, "Unit created successfully")
}
