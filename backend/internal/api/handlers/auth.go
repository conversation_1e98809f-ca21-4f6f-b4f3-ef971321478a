package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/nyunja/rentbase/backend/internal/api/dto"
	"github.com/nyunja/rentbase/backend/internal/auth"
	"github.com/nyunja/rentbase/backend/internal/config"
	"github.com/nyunja/rentbase/backend/internal/service"
	"github.com/nyunja/rentbase/backend/internal/utils"

	"go.uber.org/zap"
)

type AuthHandler struct {
	authService         *service.AuthService
	jwtService          *auth.JWTService
	refreshTokenService *auth.RefreshTokenService
	config              *config.Config
	logger              *zap.Logger
}

func NewAuthHandler(authService *service.AuthService, jwtService *auth.JWTService, refreshTokenService *auth.RefreshTokenService, config *config.Config, logger *zap.Logger) *AuthHandler {
	return &AuthHandler{
		authService:         authService,
		jwtService:          jwtService,
		refreshTokenService: refreshTokenService,
		config:              config,
		logger:              logger,
	}
}

func (h *AuthHandler) Signup(w http.ResponseWriter, r *http.Request) {
	var req dto.SignupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate request
	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	// Create user
	user, err := h.authService.CreateUser(r.Context(), req.Email, req.Password, req.Name)
	if err != nil {
		if err == service.ErrUserAlreadyExists {
			utils.WriteErrorResponse(w, http.StatusConflict, "User with this email already exists")
			return
		}
		h.logger.Error("Failed to create user", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create user")
		return
	}

	// Generate tokens
	userName := ""
	if user.Name != nil {
		userName = *user.Name
	}

	accessToken, err := h.jwtService.GenerateToken(user.ID, user.Email, userName)
	if err != nil {
		h.logger.Error("Failed to generate access token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	refreshToken, err := h.refreshTokenService.GenerateRefreshToken(r.Context(), user.ID)
	if err != nil {
		h.logger.Error("Failed to generate refresh token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Set refresh token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     h.config.JWT.RefreshTokenCookieName,
		Value:    refreshToken,
		Path:     "/",
		MaxAge:   int(h.config.JWT.RefreshTokenExpiresIn.Seconds()),
		HttpOnly: true,
		Secure:   h.config.Server.Environment == "production",
		SameSite: http.SameSiteLaxMode,
	})

	response := dto.AuthResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(h.config.JWT.ExpiresIn.Seconds()),
		User: dto.UserInfo{
			ID:    user.ID,
			Email: user.Email,
			Name:  userName,
		},
	}

	utils.WriteSuccessResponse(w, response, "User created successfully")
}

func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	var req dto.LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate request
	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	// Authenticate user
	user, err := h.authService.Authenticate(r.Context(), req.Email, req.Password)
	if err != nil {
		if err == service.ErrInvalidCredentials {
			utils.WriteErrorResponse(w, http.StatusUnauthorized, "Invalid email or password")
			return
		}
		h.logger.Error("Authentication error", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Generate tokens
	userName := ""
	if user.Name != nil {
		userName = *user.Name
	}

	accessToken, err := h.jwtService.GenerateToken(user.ID, user.Email, userName)
	if err != nil {
		h.logger.Error("Failed to generate access token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	refreshToken, err := h.refreshTokenService.GenerateRefreshToken(r.Context(), user.ID)
	if err != nil {
		h.logger.Error("Failed to generate refresh token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Set refresh token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     h.config.JWT.RefreshTokenCookieName,
		Value:    refreshToken,
		Path:     "/",
		MaxAge:   int(h.config.JWT.RefreshTokenExpiresIn.Seconds()),
		HttpOnly: true,
		Secure:   h.config.Server.Environment == "production",
		SameSite: http.SameSiteLaxMode,
	})

	response := dto.AuthResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(h.config.JWT.ExpiresIn.Seconds()),
		User: dto.UserInfo{
			ID:    user.ID,
			Email: user.Email,
			Name:  userName,
		},
	}

	utils.WriteSuccessResponse(w, response, "Login successful")
}

func (h *AuthHandler) RefreshToken(w http.ResponseWriter, r *http.Request) {
	// Get refresh token from cookie
	cookie, err := r.Cookie(h.config.JWT.RefreshTokenCookieName)
	if err != nil {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "Refresh token not found")
		return
	}

	// Validate refresh token
	userID, err := h.refreshTokenService.ValidateRefreshToken(r.Context(), cookie.Value)
	if err != nil {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "Invalid refresh token")
		return
	}

	// Get user info
	user, err := h.authService.GetUserByID(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to get user info", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Generate new access token
	accessToken, err := h.jwtService.GenerateToken(userID, user.Email, *user.Name)
	if err != nil {
		h.logger.Error("Failed to generate access token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	response := dto.AuthResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(h.config.JWT.ExpiresIn.Seconds()),
		User: dto.UserInfo{
			ID:    userID,
			Email: user.Email,
			Name:  *user.Name,
		},
	}

	utils.WriteSuccessResponse(w, response, "Token refreshed successfully")
}

func (h *AuthHandler) Logout(w http.ResponseWriter, r *http.Request) {
	// Get refresh token from cookie
	cookie, err := r.Cookie(h.config.JWT.RefreshTokenCookieName)
	if err == nil {
		// Revoke refresh token
		_ = h.refreshTokenService.RevokeRefreshToken(r.Context(), cookie.Value)
	}

	// Clear refresh token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     h.config.JWT.RefreshTokenCookieName,
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   h.config.Server.Environment == "production",
		SameSite: http.SameSiteLaxMode,
	})

	utils.WriteSuccessResponse(w, nil, "Logout successful")
}
