package service

import (
	"context"
	"errors"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/nyunja/rentbase/backend/internal/storage"
)

type LeaseService struct {
	leaseStore *storage.LeaseStore
}

func NewLeaseService(leaseStore *storage.LeaseStore) *LeaseService {
	return &LeaseService{
		leaseStore: leaseStore,
	}
}

func (s *LeaseService) CreateTenant(ctx context.Context, name string, email, phone *string) (*storage.Tenant, error) {
	if name == "" {
		return nil, errors.New("tenant name is required")
	}

	return s.leaseStore.CreateTenant(ctx, name, email, phone)
}

func (s *LeaseService) GetTenants(ctx context.Context) ([]storage.Tenant, error) {
	return s.leaseStore.GetTenants(ctx)
}

func (s *LeaseService) GetTenantByID(ctx context.Context, tenantID string) (*storage.Tenant, error) {
	if tenantID == "" {
		return nil, errors.New("tenant ID is required")
	}

	tenant, err := s.leaseStore.GetTenantByID(ctx, tenantID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("tenant not found")
		}
		return nil, err
	}

	return tenant, nil
}

func (s *LeaseService) CreateLease(ctx context.Context, unitID string, tenantID *string, startDate time.Time, endDate *time.Time, rentAmount, depositAmount float64) (*storage.Lease, error) {
	if unitID == "" {
		return nil, errors.New("unit ID is required")
	}

	if rentAmount < 0 {
		return nil, errors.New("rent amount cannot be negative")
	}

	if depositAmount < 0 {
		return nil, errors.New("deposit amount cannot be negative")
	}

	if endDate != nil && endDate.Before(startDate) {
		return nil, errors.New("end date cannot be before start date")
	}

	return s.leaseStore.CreateLease(ctx, unitID, tenantID, startDate, endDate, rentAmount, depositAmount)
}

func (s *LeaseService) GetLeasesByOwner(ctx context.Context, ownerID string, activeOnly bool) ([]storage.Lease, error) {
	if ownerID == "" {
		return nil, errors.New("owner ID is required")
	}

	return s.leaseStore.GetLeasesByOwner(ctx, ownerID, activeOnly)
}

func (s *LeaseService) GetLeaseByID(ctx context.Context, leaseID string) (*storage.Lease, error) {
	if leaseID == "" {
		return nil, errors.New("lease ID is required")
	}

	lease, err := s.leaseStore.GetLeaseByID(ctx, leaseID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("lease not found")
		}
		return nil, err
	}

	return lease, nil
}

func (s *LeaseService) DeactivateLease(ctx context.Context, leaseID string) error {
	if leaseID == "" {
		return errors.New("lease ID is required")
	}

	return s.leaseStore.DeactivateLease(ctx, leaseID)
}

func (s *LeaseService) GetActiveLeasesByOwner(ctx context.Context, ownerID string) ([]storage.Lease, error) {
	return s.GetLeasesByOwner(ctx, ownerID, true)
}

func (s *LeaseService) GetAllLeasesByOwner(ctx context.Context, ownerID string) ([]storage.Lease, error) {
	return s.GetLeasesByOwner(ctx, ownerID, false)
}