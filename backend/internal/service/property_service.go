package service

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5"
	"github.com/nyunja/rentbase/backend/internal/storage"
)

type PropertyService struct {
	propertyStore *storage.PropertyStore
}

func NewPropertyService(propertyStore *storage.PropertyStore) *PropertyService {
	return &PropertyService{
		propertyStore: propertyStore,
	}
}

func (s *PropertyService) CreateProperty(ctx context.Context, ownerID, title, address, city, country, description string) (*storage.Property, error) {
	var cityPtr, countryPtr, descPtr *string
	if city != "" {
		cityPtr = &city
	}
	if country != "" {
		countryPtr = &country
	}
	if description != "" {
		descPtr = &description
	}

	return s.propertyStore.Create(ctx, ownerID, title, address, cityPtr, countryPtr, descPtr)
}

func (s *PropertyService) GetPropertiesByOwner(ctx context.Context, ownerID string) ([]storage.Property, error) {
	return s.propertyStore.GetByOwner(ctx, ownerID)
}

func (s *PropertyService) GetPropertyByID(ctx context.Context, propertyID, ownerID string) (*storage.Property, error) {
	property, err := s.propertyStore.GetByID(ctx, propertyID, ownerID)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.New("property not found")
		}
		return nil, err
	}
	return property, nil
}

func (s *PropertyService) CreateUnit(ctx context.Context, propertyID, name string, rentAmount float64, status string) (*storage.Unit, error) {
	return s.propertyStore.CreateUnit(ctx, propertyID, name, rentAmount, status)
}

func (s *PropertyService) GetUnitsByProperty(ctx context.Context, propertyID string) ([]storage.Unit, error) {
	return s.propertyStore.GetUnitsByProperty(ctx, propertyID)
}
