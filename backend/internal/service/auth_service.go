package service

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5"
	"github.com/nyunja/rentbase/backend/internal/auth"
	"github.com/nyunja/rentbase/backend/internal/storage"
)

var (
	ErrUserAlreadyExists  = errors.New("user with this email already exists")
	ErrInvalidCredentials = errors.New("invalid email or password")
)

type AuthService struct {
	userStore *storage.UserStore
}

func NewAuthService(userStore *storage.UserStore) *AuthService {
	return &AuthService{
		userStore: userStore,
	}
}

func (s *AuthService) CreateUser(ctx context.Context, email, password, name string) (*storage.User, error) {
	// Check if user already exists
	exists, err := s.userStore.EmailExists(ctx, email)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, ErrUserAlreadyExists
	}

	// Hash password
	hashedPassword, err := auth.HashPassword(password)
	if err != nil {
		return nil, err
	}

	// Create user
	return s.userStore.Create(ctx, email, hashedPassword, name)
}

func (s *AuthService) Authenticate(ctx context.Context, email, password string) (*storage.User, error) {
	user, err := s.userStore.GetByEmail(ctx, email)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, ErrInvalidCredentials
		}
		return nil, err
	}

	if !auth.CheckPassword(password, user.PasswordHash) {
		return nil, ErrInvalidCredentials
	}

	return user, nil
}

func (s *AuthService) GetUserByID(ctx context.Context, userID string) (*storage.User, error) {
	return s.userStore.GetByID(ctx, userID)
}
