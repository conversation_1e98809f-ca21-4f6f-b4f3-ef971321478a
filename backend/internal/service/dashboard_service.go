package service

import (
	"context"

	"github.com/nyunja/rentbase/backend/internal/storage"
)

type DashboardService struct {
	dashboardRepo *storage.DashboardStore
	paymentRepo   *storage.PaymentStore
}

func NewDashboardService(dashboardRepo *storage.DashboardStore, paymentRepo *storage.PaymentStore) *DashboardService {
	return &DashboardService{
		dashboardRepo: dashboardRepo,
		paymentRepo:   paymentRepo,
	}
}

func (s *DashboardService) GetDashboardSummary(ctx context.Context, ownerID string) (map[string]interface{}, error) {
	// Get basic stats using the SQL function
	stats, err := s.dashboardRepo.GetStats(ctx, ownerID)
	if err != nil {
		return nil, err
	}

	// Get recent activity
	recentActivity, err := s.dashboardRepo.GetRecentActivity(ctx, ownerID, 10)
	if err != nil {
		return nil, err
	}

	// Get recent payments
	recentPayments, err := s.paymentRepo.GetRecent(ctx, ownerID, 5)
	if err != nil {
		return nil, err
	}

	// Get overdue leases
	overdueLeases, err := s.dashboardRepo.GetOverdueLeases(ctx, ownerID)
	if err != nil {
		return nil, err
	}

	// Get unit occupancy using the view
	unitOccupancy, err := s.dashboardRepo.GetUnitOccupancy(ctx, ownerID)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"stats":           stats,
		"recent_activity": recentActivity,
		"recent_payments": recentPayments,
		"overdue_leases":  overdueLeases,
		"unit_occupancy":  unitOccupancy,
	}, nil
}
