package utils

import (
    "encoding/json"
    "net/http"
)

type ErrorResponse struct {
    Error   string `json:"error"`
    Message string `json:"message,omitempty"`
    Code    int    `json:"code"`
}

type SuccessResponse struct {
    Data    interface{} `json:"data,omitempty"`
    Message string      `json:"message,omitempty"`
}

func WriteJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
    w.Header().Set("Content-Type", "application/json")
    w.WriteHeader(statusCode)
    
    if err := json.NewEncoder(w).Encode(data); err != nil {
        http.Error(w, "Failed to encode response", http.StatusInternalServerError)
    }
}

func WriteErrorResponse(w http.ResponseWriter, statusCode int, message string) {
    response := ErrorResponse{
        Error:   http.StatusText(statusCode),
        Message: message,
        Code:    statusCode,
    }
    WriteJSONResponse(w, statusCode, response)
}

func WriteSuccessResponse(w http.ResponseWriter, data interface{}, message ...string) {
    response := SuccessResponse{
        Data: data,
    }
    
    if len(message) > 0 {
        response.Message = message[0]
    }
    
    WriteJSONResponse(w, http.StatusOK, response)
}
