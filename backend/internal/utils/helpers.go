package utils

import (
    "net"
    "net/http"
    "strings"
)

// GetClientIP gets the real IP address of the client
func GetClientIP(r *http.Request) string {
    // Check X-Forwarded-For header
    xForwardedFor := r.Header.Get("X-Forwarded-For")
    if xForwardedFor != "" {
        // Take the first IP if multiple IPs are present
        ips := strings.Split(xForwardedFor, ",")
        return strings.TrimSpace(ips[0])
    }

    // Check X-Real-IP header
    xRealIP := r.Header.Get("X-Real-IP")
    if xRealIP != "" {
        return xRealIP
    }

    // Fall back to RemoteAddr
    ip, _, err := net.SplitHostPort(r.RemoteAddr)
    if err != nil {
        return r.RemoteAddr
    }
    return ip
}