package utils

import (
    "regexp"
    "strings"
)

var (
    emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
    phoneRegex = regexp.MustCompile(`^\+?[1-9]\d{1,14}$`)
)

type ValidationError struct {
    Field   string `json:"field"`
    Message string `json:"message"`
}

type ValidationErrors []ValidationError

func (v ValidationErrors) Error() string {
    var messages []string
    for _, err := range v {
        messages = append(messages, err.Field+": "+err.Message)
    }
    return strings.Join(messages, ", ")
}

func ValidateEmail(email string) ValidationError {
    if email == "" {
        return ValidationError{Field: "email", Message: "Email is required"}
    }
    if !emailRegex.MatchString(email) {
        return ValidationError{Field: "email", Message: "Invalid email format"}
    }
    return ValidationError{}
}

func ValidatePassword(password string) ValidationError {
    if password == "" {
        return ValidationError{Field: "password", Message: "Password is required"}
    }
    if len(password) < 8 {
        return ValidationError{Field: "password", Message: "Password must be at least 8 characters"}
    }
    return ValidationError{}
}

func ValidateRequired(value, fieldName string) ValidationError {
    if strings.TrimSpace(value) == "" {
        return ValidationError{Field: fieldName, Message: fieldName + " is required"}
    }
    return ValidationError{}
}

func ValidatePhone(phone string) ValidationError {
    if phone != "" && !phoneRegex.MatchString(phone) {
        return ValidationError{Field: "phone", Message: "Invalid phone number format"}
    }
    return ValidationError{}
}
