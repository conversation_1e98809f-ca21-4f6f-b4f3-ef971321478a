package utils

import (
    "os"

    "go.uber.org/zap"
    "go.uber.org/zap/zapcore"
)

func NewLogger() *zap.Logger {
    environment := os.Getenv("ENVIRONMENT")
    logLevel := os.Getenv("LOG_LEVEL")
    logFormat := os.Getenv("LOG_FORMAT")

    var config zap.Config

    if environment == "production" {
        config = zap.NewProductionConfig()
    } else {
        config = zap.NewDevelopmentConfig()
    }

    // Set log level
    switch logLevel {
    case "debug":
        config.Level = zap.NewAtomicLevelAt(zap.DebugLevel)
    case "info":
        config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
    case "warn":
        config.Level = zap.NewAtomicLevelAt(zap.WarnLevel)
    case "error":
        config.Level = zap.NewAtomicLevelAt(zap.ErrorLevel)
    default:
        config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
    }

    // Set output format
    if logFormat == "text" {
        config.Encoding = "console"
        config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
    }

    logger, err := config.Build()
    if err != nil {
        panic(err)
    }

    return logger
}