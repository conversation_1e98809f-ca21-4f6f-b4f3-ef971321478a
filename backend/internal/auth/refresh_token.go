package auth

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

type RefreshTokenService struct {
	db        *pgxpool.Pool
	expiresIn time.Duration
}

func NewRefreshTokenService(db *pgxpool.Pool, expiresIn time.Duration) *RefreshTokenService {
	return &RefreshTokenService{
		db:        db,
		expiresIn: expiresIn,
	}
}

func (r *RefreshTokenService) GenerateRefreshToken(ctx context.Context, userID string) (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	token := base64.URLEncoding.EncodeToString(bytes)

	// Hash token for storage
	hash := sha256.Sum256([]byte(token))
	tokenHash := fmt.Sprintf("%x", hash)

	// Store in database
	query := `
        INSERT INTO refresh_tokens (user_id, token_hash, expires_at)
        VALUES ($1, $2, $3)`

	_, err := r.db.Exec(ctx, query, userID, tokenHash, time.Now().Add(r.expiresIn))
	if err != nil {
		return "", err
	}

	return token, nil
}

func (r *RefreshTokenService) ValidateRefreshToken(ctx context.Context, token string) (string, error) {
    // Hash token
    hash := sha256.Sum256([]byte(token))
    tokenHash := fmt.Sprintf("%x", hash)
    
    // Check in database
    var userID string
    query := `
        SELECT user_id FROM refresh_tokens 
        WHERE token_hash = $1 AND expires_at > NOW() AND revoked = false`
    
    err := r.db.QueryRow(ctx, query, tokenHash).Scan(&userID)
    if err != nil {
        return "", err
    }
    
    return userID, nil
}

func (r *RefreshTokenService) RevokeRefreshToken(ctx context.Context, token string) error {
    // Hash token
    hash := sha256.Sum256([]byte(token))
    tokenHash := fmt.Sprintf("%x", hash)
    
    query := `UPDATE refresh_tokens SET revoked = true WHERE token_hash = $1`
    _, err := r.db.Exec(ctx, query, tokenHash)
    return err
}
