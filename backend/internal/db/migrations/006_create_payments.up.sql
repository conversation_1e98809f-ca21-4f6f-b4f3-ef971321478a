CREATE TABLE IF NOT EXISTS payments (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    lease_id uuid NOT NULL REFERENCES leases(id) ON DELETE CASCADE,
    amount numeric(12,2) NOT NULL CHECK (amount > 0),
    paid_at timestamptz NOT NULL DEFAULT now(),
    payment_method text DEFAULT 'manual' CHECK (payment_method IN ('manual', 'mpesa', 'card', 'bank_transfer', 'cash', 'check')),
    notes text,
    created_by uuid REFERENCES users(id),
    created_at timestamptz NOT NULL DEFAULT now()
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_payments_lease_id ON payments(lease_id);
CREATE INDEX idx_payments_paid_at ON payments(paid_at);
CREATE INDEX idx_payments_created_by ON payments(created_by);
CREATE INDEX idx_payments_method ON payments(payment_method);
