-- View for unit occupancy status with current lease info
CREATE VIEW unit_occupancy AS
SELECT 
    u.id as unit_id,
    u.property_id,
    u.name as unit_name,
    u.rent_amount,
    u.status,
    l.id as current_lease_id,
    l.tenant_id,
    t.name as tenant_name,
    t.email as tenant_email,
    l.start_date,
    l.end_date,
    CASE 
        WHEN l.id IS NULL THEN 'vacant'
        WHEN l.end_date IS NOT NULL AND l.end_date < CURRENT_DATE THEN 'expired'
        WHEN l.active = false THEN 'inactive'
        ELSE 'occupied'
    END as occupancy_status
FROM units u
LEFT JOIN leases l ON u.id = l.unit_id AND l.active = true
LEFT JOIN tenants t ON l.tenant_id = t.id;

-- View for payment summary by lease
CREATE VIEW lease_payment_summary AS
SELECT 
    l.id as lease_id,
    l.unit_id,
    l.tenant_id,
    l.rent_amount,
    COUNT(p.id) as payment_count,
    COALESCE(SUM(p.amount), 0) as total_paid,
    MAX(p.paid_at) as last_payment_date,
    (l.rent_amount * EXTRACT(MONTH FROM AGE(COALESCE(l.end_date, CURRENT_DATE), l.start_date))) as expected_total
FROM leases l
LEFT JOIN payments p ON l.id = p.lease_id
GROUP BY l.id, l.unit_id, l.tenant_id, l.rent_amount, l.start_date, l.end_date;

-- View for maintenance request summary by property
CREATE VIEW property_maintenance_summary AS
SELECT 
    p.id as property_id,
    p.title as property_title,
    COUNT(mr.id) as total_requests,
    COUNT(CASE WHEN mr.status = 'pending' THEN 1 END) as pending_requests,
    COUNT(CASE WHEN mr.status = 'in_progress' THEN 1 END) as in_progress_requests,
    COUNT(CASE WHEN mr.status = 'resolved' THEN 1 END) as resolved_requests,
    COALESCE(SUM(mr.cost), 0) as total_maintenance_cost
FROM properties p
LEFT JOIN units u ON p.id = u.property_id
LEFT JOIN maintenance_requests mr ON u.id = mr.unit_id
GROUP BY p.id, p.title;