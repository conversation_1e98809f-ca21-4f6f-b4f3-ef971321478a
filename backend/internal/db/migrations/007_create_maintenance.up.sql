CREATE TABLE IF NOT EXISTS maintenance_requests (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id uuid REFERENCES units(id) ON DELETE SET NULL,
    lease_id uuid REFERENCES leases(id) ON DELETE SET NULL,
    title text NOT NULL,
    description text,
    status text NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'resolved', 'cancelled')),
    priority text DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    created_by uuid REFERENCES users(id),
    assigned_to text,
    cost numeric(12,2) DEFAULT 0 CHECK (cost >= 0),
    created_at timestamptz NOT NULL DEFAULT now(),
    resolved_at timestamptz,
    
    -- At least one of unit_id or lease_id must be provided
    CONSTRAINT chk_maintenance_reference CHECK (unit_id IS NOT NULL OR lease_id IS NOT NULL)
);

-- Create indexes
CREATE INDEX idx_maintenance_unit_id ON maintenance_requests(unit_id);
CREATE INDEX idx_maintenance_lease_id ON maintenance_requests(lease_id);
CREATE INDEX idx_maintenance_status ON maintenance_requests(status);
CREATE INDEX idx_maintenance_priority ON maintenance_requests(priority);
CREATE INDEX idx_maintenance_created_by ON maintenance_requests(created_by);
CREATE INDEX idx_maintenance_created_at ON maintenance_requests(created_at);
