CREATE TABLE attachments (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_type text NOT NULL CHECK (parent_type IN ('property', 'unit', 'lease', 'maintenance', 'payment')),
    parent_id uuid NOT NULL,
    url text NOT NULL,
    filename text,
    file_size bigint,
    content_type text,
    uploaded_by uuid REFERENCES users(id),
    uploaded_at timestamptz NOT NULL DEFAULT now()
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_attachments_parent ON attachments(parent_type, parent_id);
CREATE INDEX idx_attachments_uploaded_by ON attachments(uploaded_by);
CREATE INDEX idx_attachments_uploaded_at ON attachments(uploaded_at);
