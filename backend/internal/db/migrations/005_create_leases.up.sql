
CREATE TABLE IF NOT EXISTS leases (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    unit_id uuid NOT NULL REFERENCES units(id) ON DELETE CASCADE,
    tenant_id uuid REFERENCES tenants(id) ON DELETE SET NULL,
    start_date date NOT NULL,
    end_date date,
    rent_amount numeric(12,2) NOT NULL CHECK (rent_amount >= 0),
    deposit_amount numeric(12,2) DEFAULT 0 CHECK (deposit_amount >= 0),
    active boolean DEFAULT true,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    
    -- Ensure end_date is after start_date
    CONSTRAINT chk_lease_dates CHECK (end_date IS NULL OR end_date >= start_date)
);

-- Create indexes
CREATE INDEX idx_leases_unit_id ON leases(unit_id);
CREATE INDEX idx_leases_tenant_id ON leases(tenant_id);
CREATE INDEX idx_leases_active ON leases(active);
CREATE INDEX idx_leases_dates ON leases(start_date, end_date);

-- <PERSON>reate trigger for updated_at
CREATE TRIGGER update_leases_updated_at 
    BEFORE UPDATE ON leases 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
