-- Insert sample data for development (only if no users exist)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM users LIMIT 1) THEN
        -- Insert sample landlord (password: 'password')
        INSERT INTO users (id, email, password_hash, name) VALUES
        ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON>lord');
        
        -- Insert sample property
        INSERT INTO properties (id, owner_id, title, address, city, country, description) VALUES
        ('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-************', 'Sunset Apartments', '123 Main St', 'Nairobi', 'Kenya', 'Modern apartment building with 4 units');
        
        -- Insert sample units
        INSERT INTO units (id, property_id, name, rent_amount, status) VALUES
        ('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'Unit 1A', 25000.00, 'occupied'),
        ('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', 'Unit 1B', 25000.00, 'vacant'),
        ('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440001', 'Unit 2A', 30000.00, 'occupied'),
        ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440001', 'Unit 2B', 30000.00, 'maintenance');
        
        -- Insert sample tenant
        INSERT INTO tenants (id, name, email, phone) VALUES
        ('550e8400-e29b-41d4-a716-446655440006', 'Jane Smith', '<EMAIL>', '+254700123456'),
        ('550e8400-e29b-41d4-a716-446655440007', 'Bob Wilson', '<EMAIL>', '+254700123457');
        
        -- Insert sample leases
        INSERT INTO leases (id, unit_id, tenant_id, start_date, end_date, rent_amount, deposit_amount, active) VALUES
        ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440006', '2024-01-01', '2024-12-31', 25000.00, 50000.00, true),
        ('550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440007', '2024-02-01', '2025-01-31', 30000.00, 60000.00, true);
        
        -- Insert sample payments
        INSERT INTO payments (lease_id, amount, paid_at, payment_method, created_by) VALUES
        ('550e8400-e29b-41d4-a716-************', 25000.00, '2024-01-05', 'mpesa', '550e8400-e29b-41d4-a716-************'),
        ('550e8400-e29b-41d4-a716-************', 25000.00, '2024-02-05', 'mpesa', '550e8400-e29b-41d4-a716-************'),
        ('550e8400-e29b-41d4-a716-************', 30000.00, '2024-02-05', 'bank_transfer', '550e8400-e29b-41d4-a716-************');
        
        -- Insert sample maintenance request
        INSERT INTO maintenance_requests (unit_id, title, description, status, priority, created_by) VALUES
        ('550e8400-e29b-41d4-a716-************', 'Leaky faucet', 'Kitchen faucet is dripping constantly', 'pending', 'medium', '550e8400-e29b-41d4-a716-************');
    END IF;
END $$;