-- Function to calculate rent due for a lease
CREATE OR REPLACE FUNCTION calculate_rent_due(lease_uuid uuid, as_of_date date DEFAULT CURRENT_DATE)
RETURNS numeric AS $$
DECLARE
    lease_record leases%ROWTYPE;
    months_elapsed integer;
    total_due numeric;
    total_paid numeric;
BEGIN
    -- Get lease details
    SELECT * INTO lease_record FROM leases WHERE id = lease_uuid;
    
    IF NOT FOUND THEN
        RETURN 0;
    END IF;
    
    -- Calculate months elapsed
    months_elapsed := EXTRACT(MONTH FROM AGE(
        LEAST(as_of_date, COALESCE(lease_record.end_date, as_of_date)), 
        lease_record.start_date
    ));
    
    -- Calculate total due
    total_due := lease_record.rent_amount * GREATEST(months_elapsed, 0);
    
    -- Get total paid
    SELECT COALESCE(SUM(amount), 0) 
    INTO total_paid 
    FROM payments 
    WHERE lease_id = lease_uuid AND paid_at::date <= as_of_date;
    
    RETURN GREATEST(total_due - total_paid, 0);
END;
$$ LANGUAGE plpgsql;

-- Function to get property dashboard stats
CREATE OR REPLACE FUNCTION get_property_dashboard_stats(owner_uuid uuid)
RETURNS TABLE (
    total_properties bigint,
    total_units bigint,
    occupied_units bigint,
    vacant_units bigint,
    total_active_leases bigint,
    total_monthly_rent numeric,
    pending_maintenance bigint,
    overdue_payments bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT p.id) as total_properties,
        COUNT(DISTINCT u.id) as total_units,
        COUNT(DISTINCT CASE WHEN uo.occupancy_status = 'occupied' THEN u.id END) as occupied_units,
        COUNT(DISTINCT CASE WHEN uo.occupancy_status = 'vacant' THEN u.id END) as vacant_units,
        COUNT(DISTINCT CASE WHEN l.active = true THEN l.id END) as total_active_leases,
        COALESCE(SUM(DISTINCT CASE WHEN l.active = true THEN l.rent_amount END), 0) as total_monthly_rent,
        COUNT(DISTINCT CASE WHEN mr.status = 'pending' THEN mr.id END) as pending_maintenance,
        COUNT(DISTINCT CASE WHEN calculate_rent_due(l.id) > 0 THEN l.id END) as overdue_payments
    FROM properties p
    LEFT JOIN units u ON p.id = u.property_id
    LEFT JOIN unit_occupancy uo ON u.id = uo.unit_id
    LEFT JOIN leases l ON u.id = l.unit_id AND l.active = true
    LEFT JOIN maintenance_requests mr ON u.id = mr.unit_id
    WHERE p.owner_id = owner_uuid;
END;
$$ LANGUAGE plpgsql;
