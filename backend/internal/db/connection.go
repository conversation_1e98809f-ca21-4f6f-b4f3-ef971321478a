package db

import (
    "context"
    "fmt"
    "time"

    "github.com/nyunja/rentbase/backend/internal/config"

    "github.com/jackc/pgx/v5/pgxpool"
    "github.com/golang-migrate/migrate/v4"
    _ "github.com/golang-migrate/migrate/v4/database/postgres"
    _ "github.com/golang-migrate/migrate/v4/source/file"
)

// NewConnection creates a new database connection pool
func NewConnection(cfg config.DatabaseConfig) (*pgxpool.Pool, error) {
    config, err := pgxpool.ParseConfig(cfg.URL)
    if err != nil {
        return nil, fmt.Errorf("failed to parse database URL: %w", err)
    }

    // Configure connection pool
    config.MaxConns = int32(cfg.MaxConnections)
    config.MinConns = int32(cfg.MaxIdleConns)
    config.MaxConnLifetime = cfg.MaxLifetime
    config.MaxConnIdleTime = 30 * time.Minute

    // Create connection pool
    pool, err := pgxpool.New(context.Background(), config.ConnString())
    if err != nil {
        return nil, fmt.Errorf("failed to create connection pool: %w", err)
    }

    // Test connection
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()

    if err := pool.Ping(ctx); err != nil {
        pool.Close()
        return nil, fmt.Errorf("failed to ping database: %w", err)
    }

    return pool, nil
}

// RunMigrations runs database migrations
func RunMigrations(databaseURL, migrationsPath string) error {
    m, err := migrate.New(
        fmt.Sprintf("file://%s", migrationsPath),
        databaseURL)
    if err != nil {
        return fmt.Errorf("failed to create migrate instance: %w", err)
    }
    defer m.Close()

    if err := m.Up(); err != nil && err != migrate.ErrNoChange {
        return fmt.Errorf("failed to run migrations: %w", err)
    }

    return nil
}