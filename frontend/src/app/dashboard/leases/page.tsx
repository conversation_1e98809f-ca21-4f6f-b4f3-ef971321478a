'use client';

import { useEffect, useState } from 'react';
import { leasesApi } from '@/lib/api';
import { Lease } from '@/types';
import { FileText, Plus, Calendar, DollarSign, User } from 'lucide-react';

export default function LeasesPage() {
  const [leases, setLeases] = useState<Lease[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLeases = async () => {
      const token = localStorage.getItem('access_token');
      if (!token) return;

      try {
        const response = await leasesApi.getLeases(token);
        if (response.success && response.data) {
          setLeases(response.data);
        }
      } catch (error) {
        console.error('Failed to fetch leases:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLeases();
  }, []);

  if (loading) {
    return <div>Loading leases...</div>;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Leases</h1>
          <p className="text-gray-600">Manage lease agreements and terms</p>
        </div>
        <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Create Lease
        </button>
      </div>

      {/* Leases List */}
      {leases.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No leases</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating a new lease agreement.</p>
          <div className="mt-6">
            <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Lease
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {leases.map((lease) => (
            <div key={lease.id} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <FileText className="h-6 w-6 text-gray-400" />
                    <h3 className="ml-2 text-lg font-medium text-gray-900">
                      {lease.unit_name || `Unit ${lease.unit_id.slice(-4)}`}
                    </h3>
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    lease.active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {lease.active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                
                <div className="space-y-3">
                  {lease.tenant_name && (
                    <div className="flex items-center text-sm text-gray-600">
                      <User className="h-4 w-4 mr-2" />
                      <span>{lease.tenant_name}</span>
                      {lease.tenant_email && (
                        <span className="ml-2 text-gray-500">({lease.tenant_email})</span>
                      )}
                    </div>
                  )}
                  
                  <div className="flex items-center text-sm text-gray-600">
                    <DollarSign className="h-4 w-4 mr-2" />
                    <span>Rent: {formatCurrency(lease.rent_amount)}/month</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-600">
                    <DollarSign className="h-4 w-4 mr-2" />
                    <span>Deposit: {formatCurrency(lease.deposit_amount)}</span>
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>
                      {formatDate(lease.start_date)}
                      {lease.end_date && ` - ${formatDate(lease.end_date)}`}
                    </span>
                  </div>
                </div>

                <div className="mt-6 flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    Created {formatDate(lease.created_at)}
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-500 text-sm font-medium">
                      View Details
                    </button>
                    {lease.active && (
                      <button className="text-red-600 hover:text-red-500 text-sm font-medium">
                        Deactivate
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}