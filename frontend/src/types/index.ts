// Auth related types
export interface User {
  id: string;
  email: string;
  name: string;
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface SignupRequest {
  email: string;
  password: string;
  name: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

// Property related types
export interface Property {
  id: string;
  owner_id: string;
  title: string;
  address: string;
  city?: string;
  country?: string;
  description?: string;
  units_count: number;
  created_at: string;
  updated_at: string;
}

export interface CreatePropertyRequest {
  title: string;
  address: string;
  city?: string;
  country?: string;
  description?: string;
}

export interface Unit {
  id: string;
  property_id: string;
  name: string;
  rent_amount: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUnitRequest {
  name: string;
  rent_amount: number;
  status?: string;
}

// Tenant and Lease related types
export interface Tenant {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateTenantRequest {
  name: string;
  email?: string;
  phone?: string;
}

export interface Lease {
  id: string;
  unit_id: string;
  unit_name?: string;
  property_id?: string;
  tenant_id?: string;
  tenant_name?: string;
  tenant_email?: string;
  start_date: string;
  end_date?: string;
  rent_amount: number;
  deposit_amount: number;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateLeaseRequest {
  unit_id: string;
  tenant_id?: string;
  start_date: string;
  end_date?: string;
  rent_amount: number;
  deposit_amount: number;
}

// Dashboard related types
export interface DashboardStats {
  total_properties: number;
  total_units: number;
  occupied_units: number;
  vacant_units: number;
  active_leases: number;
  total_monthly_rent: number;
  pending_maintenance: number;
  overdue_payments: number;
}

export interface Payment {
  id: string;
  tenantName: string;
  property: string;
  amount: number;
  date: string;
  status: 'paid' | 'pending' | 'overdue';
}

export interface MaintenanceRequest {
  id: string;
  issue: string;
  property: string;
  tenantName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
}