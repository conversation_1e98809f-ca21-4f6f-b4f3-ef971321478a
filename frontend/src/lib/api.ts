const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1';

interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

async function apiRequest<T>(
  endpoint: string,
  options?: RequestInit
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP error! status: ${response.status}`,
      };
    }

    const data = await response.json();
    return {
      success: true,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export const api = {
  get: <T>(endpoint: string, token?: string) => 
    apiRequest<T>(endpoint, {
      headers: token ? { Authorization: `Bearer ${token}` } : undefined,
    }),
  post: <T>(endpoint: string, data: any, token?: string) =>
    apiRequest<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: token ? { Authorization: `Bearer ${token}` } : undefined,
    }),
  put: <T>(endpoint: string, data: any, token?: string) =>
    apiRequest<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      headers: token ? { Authorization: `Bearer ${token}` } : undefined,
    }),
  delete: <T>(endpoint: string, token?: string) =>
    apiRequest<T>(endpoint, { 
      method: 'DELETE',
      headers: token ? { Authorization: `Bearer ${token}` } : undefined,
    }),
};

// Specific API endpoints based on backend routes
export const authApi = {
  signup: (data: any) => api.post('/auth/signup', data),
  login: (data: any) => api.post('/auth/login', data),
  refresh: (data: any) => api.post('/auth/refresh', data),
  logout: (data: any) => api.post('/auth/logout', data),
};

export const propertiesApi = {
  getProperties: (token: string) => api.get('/properties', token),
  createProperty: (data: any, token: string) => api.post('/properties', data, token),
  getProperty: (id: string, token: string) => api.get(`/properties/${id}`, token),
  createUnit: (propertyId: string, data: any, token: string) => 
    api.post(`/properties/${propertyId}/units`, data, token),
  getUnits: (propertyId: string, token: string) => 
    api.get(`/properties/${propertyId}/units`, token),
};

export const leasesApi = {
  getLeases: (token: string) => api.get('/leases', token),
  createLease: (data: any, token: string) => api.post('/leases', data, token),
  getLease: (id: string, token: string) => api.get(`/leases/${id}`, token),
  deactivateLease: (id: string, token: string) => api.put(`/leases/${id}/deactivate`, {}, token),
};

export const tenantsApi = {
  getTenants: (token: string) => api.get('/tenants', token),
  createTenant: (data: any, token: string) => api.post('/tenants', data, token),
  getTenant: (id: string, token: string) => api.get(`/tenants/${id}`, token),
};

export const dashboardApi = {
  getSummary: (token: string) => api.get('/dashboard/summary', token),
  getStats: (token: string) => api.get('/dashboard/stats', token),
};

export default api;