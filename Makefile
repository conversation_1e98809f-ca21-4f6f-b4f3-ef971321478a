
.PHONY: help build run clean docker-build docker-run docker-stop logs test frontend-dev frontend-build frontend-install

# Default target
help:
	@echo "Available commands:"
	@echo "  build            - Build the Go application"
	@echo "  run              - Run the application locally"
	@echo "  clean            - Clean build artifacts"
	@echo "  test             - Run tests"
	@echo ""
	@echo "Frontend commands:"
	@echo "  frontend-install - Install frontend dependencies"
	@echo "  frontend-dev     - Run frontend in development mode"
	@echo "  frontend-build   - Build frontend for production"
	@echo ""
	@echo "Docker commands:"
	@echo "  docker-build     - Build Docker images"
	@echo "  docker-run       - Run with Docker Compose"
	@echo "  docker-stop      - Stop Docker containers"
	@echo "  logs             - View Docker logs"
	@echo "  dev              - Start development environment"
	@echo "  destroy          - Remove all containers and volumes"

# Go commands
build:
	cd backend && go build -o bin/server cmd/server/main.go

run:
	cd backend && go run cmd/server/main.go

clean:
	cd backend && rm -rf bin/

test:
	cd backend && go test ./...

# Frontend commands
frontend-install:
	cd frontend && npm install

frontend-dev:
	cd frontend && npm run dev

frontend-build:
	cd frontend && npm run build

# Docker commands
docker-build:
	docker-compose build

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

logs:
	docker-compose logs -f

# Development helpers
dev: docker-run
	@echo "Development environment started"
	@echo "Backend: http://localhost:8080"
	@echo "Database: localhost:5432"
	@echo "Adminer: http://localhost:8081 (run 'docker-compose --profile admin-tools up -d' first)"

destroy:
	docker-compose down -v
	docker system prune -f