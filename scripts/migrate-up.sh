#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running database migrations...${NC}"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}Error: docker-compose.yml not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Check if backend container is running
if ! docker-compose ps backend | grep -q "Up"; then
    echo -e "${RED}Error: Backend container is not running. Please start it first with 'make dev'${NC}"
    exit 1
fi

# Check migration status
echo -e "${YELLOW}Checking current migration status...${NC}"
docker-compose exec db psql -U postgres -d rental_mvp -c "SELECT version, dirty FROM schema_migrations;" || {
    echo -e "${RED}Error: Could not connect to database or schema_migrations table doesn't exist${NC}"
    exit 1
}

# Run migrations through the backend application
echo -e "${YELLOW}Restarting backend to trigger migrations...${NC}"
docker-compose restart backend

# Wait for backend to be ready
echo -e "${YELLOW}Waiting for backend to be ready...${NC}"
sleep 5

# Check if migrations completed successfully
if docker-compose logs backend --tail=10 | grep -q "Database migrations completed successfully"; then
    echo -e "${GREEN}✅ Migrations completed successfully!${NC}"

    # Show final migration status
    echo -e "${YELLOW}Final migration status:${NC}"
    docker-compose exec db psql -U postgres -d rental_mvp -c "SELECT version, dirty FROM schema_migrations;"

    # Show created tables
    echo -e "${YELLOW}Created tables:${NC}"
    docker-compose exec db psql -U postgres -d rental_mvp -c "\dt"
else
    echo -e "${RED}❌ Migration may have failed. Check logs:${NC}"
    docker-compose logs backend --tail=20
    exit 1
fi

echo -e "${GREEN}Migration script completed!${NC}"