#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${RED}⚠️  WARNING: This will rollback database migrations!${NC}"
echo -e "${YELLOW}This operation can cause data loss. Are you sure? (y/N)${NC}"
read -r response

if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Migration rollback cancelled.${NC}"
    exit 0
fi

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}Error: docker-compose.yml not found. Please run this script from the project root.${NC}"
    exit 1
fi

# Get current migration version
echo -e "${YELLOW}Checking current migration status...${NC}"
CURRENT_VERSION=$(docker-compose exec db psql -U postgres -d rental_mvp -t -c "SELECT version FROM schema_migrations;" | tr -d ' ')

if [ -z "$CURRENT_VERSION" ]; then
    echo -e "${RED}Error: Could not determine current migration version${NC}"
    exit 1
fi

echo -e "${YELLOW}Current migration version: $CURRENT_VERSION${NC}"

# Ask for target version
echo -e "${YELLOW}Enter target version to rollback to (or press Enter to rollback by 1):${NC}"
read -r target_version

if [ -z "$target_version" ]; then
    target_version=$((CURRENT_VERSION - 1))
fi

echo -e "${YELLOW}Rolling back to version: $target_version${NC}"

# Note: Since we're using golang-migrate through the app, we need to manually run down migrations
# This is a simplified approach - in production you'd want more sophisticated migration management

echo -e "${RED}❌ Manual migration rollback required${NC}"
echo -e "${YELLOW}To rollback migrations manually:${NC}"
echo "1. Connect to the database:"
echo "   docker-compose exec db psql -U postgres -d rental_mvp"
echo ""
echo "2. Run the down migration SQL files manually in reverse order"
echo "   (from backend/internal/db/migrations/)"
echo ""
echo "3. Update schema_migrations table:"
echo "   UPDATE schema_migrations SET version = $target_version, dirty = false;"
echo ""
echo -e "${YELLOW}For a complete reset, use: docker-compose down -v && docker-compose up -d${NC}"