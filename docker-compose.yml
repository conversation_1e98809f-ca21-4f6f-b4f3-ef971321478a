version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:13-alpine
    container_name: rental_mvp_db
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: rental_mvp
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5433:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - rental_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d rental_mvp"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Go Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: rental_mvp_backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    env_file:
      - ./backend/.env.development
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - backend_uploads:/root/uploads
    networks:
      - rental_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:8080/api/v1/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: rental_mvp_frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - ./frontend/.env.local
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - rental_network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis (optional - for caching)
  redis:
    image: redis:7-alpine
    container_name: rental_mvp_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - rental_network
    profiles: ["with-redis"]

  # Database Admin Tool
  adminer:
    image: adminer:latest
    container_name: rental_mvp_adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    depends_on:
      - db
    networks:
      - rental_network
    profiles: ["admin-tools"]

volumes:
  db_data:
  backend_uploads:
  redis_data:

networks:
  rental_network:
    driver: bridge
