# RentBase - Rental Property Management MVP

A modern, secure rental property management system built with Go, Next.js, and PostgreSQL. Designed for landlords to manage properties, tenants, leases, payments, and maintenance requests efficiently.

## 🏗️ Architecture

- **Backend**: Go with Chi router, PostgreSQL with sqlc, JWT authentication
- **Frontend**: Next.js 14+ with TypeScript, Tailwind CSS, React Query
- **Database**: PostgreSQL with UUID primary keys and comprehensive relationships
- **Storage**: S3-compatible for file attachments
- **Development**: Docker Compose for local development environment

## ✨ Features

### MVP Core Features
- 🏠 **Property Management**: Add and manage rental properties with multiple units
- 👥 **Tenant Management**: Store tenant information and contact details
- 📋 **Lease Management**: Create and track lease agreements with terms and status
- 💳 **Payment Tracking**: Record and monitor rent payments with history
- 🔧 **Maintenance Requests**: Submit, track, and resolve maintenance issues
- 📊 **Dashboard**: Overview of occupancy, payments, and maintenance status
- 🔐 **Authentication**: Secure JWT-based auth with refresh tokens

### Security Features
- Password hashing with bcrypt
- JWT access tokens (15min) + HTTP-only refresh cookies
- CORS protection and rate limiting
- Input validation and SQL injection protection
- User-scoped data access (landlords see only their properties)

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### 1. Clone and Setup
```bash
git clone <repository-url>
cd rental-property-mvp
cp .env.example .env
```

### 2. Start Development Environment
```bash
# Start all services (database, backend, frontend)
docker-compose up

# Or start in detached mode
docker-compose up -d
```

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **Database**: localhost:5432 (postgres/password)

### 4. Test Credentials
The application includes sample data for development:
- **Email**: `<EMAIL>`
- **Password**: `password`

You can also create new accounts via the signup endpoint.

### 4. Run Database Migrations
```bash
# Migrations run automatically on backend startup
# To run manually:
docker-compose exec backend ./scripts/migrate-up.sh
```

## 📁 Project Structure

```
rental-property-mvp/
├── backend/                 # Go API server
│   ├── cmd/server/         # Application entrypoint
│   ├── internal/           # Private application code
│   │   ├── api/           # HTTP handlers and routes
│   │   ├── auth/          # Authentication logic
│   │   ├── db/            # Database queries and migrations
│   │   ├── service/       # Business logic layer
│   │   └── utils/         # Utilities and helpers
│   └── tests/             # Backend tests
├── frontend/               # Next.js application
│   ├── src/app/           # App Router pages
│   ├── src/components/    # React components
│   ├── src/hooks/         # Custom hooks
│   ├── src/lib/           # Utilities and API client
│   └── src/types/         # TypeScript definitions
├── docs/                  # Documentation
└── scripts/               # Development scripts
```

## 🛠️ Development

### Backend Development
```bash
# Install dependencies
cd backend && go mod tidy

# Generate database code (after schema changes)
sqlc generate

# Run tests
go test ./...

# Run with hot reload (Air)
air
```

### Frontend Development
```bash
# Install dependencies
cd frontend && npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build
```

### Database Operations
```bash
# Create new migration
migrate create -ext sql -dir backend/internal/db/migrations <migration_name>

# Run migrations up
./scripts/migrate-up.sh

# Run migrations down
./scripts/migrate-down.sh

# Reset database (careful!)
docker-compose down -v && docker-compose up db
```

## 🔧 Configuration

### Environment Variables

**Backend (.env or backend/.env.development)**
```env
# Database
DATABASE_URL=************************************/rental_mvp?sslmode=disable

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# Server
PORT=8080
ENVIRONMENT=development

# Storage (optional for MVP)
S3_BUCKET=rental-attachments
S3_REGION=us-east-1
S3_ACCESS_KEY=
S3_SECRET_KEY=
```

**Frontend (.env.local)**
```env
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_APP_NAME=Rental Manager
```

## 📡 API Documentation

### Authentication Endpoints
```
POST /api/v1/auth/signup    - Register new landlord
POST /api/v1/auth/login     - Login with email/password
POST /api/v1/auth/refresh   - Refresh access token
POST /api/v1/auth/logout    - Logout and revoke tokens
```

### Core Endpoints
```
# Properties
GET    /api/v1/properties           - List user's properties
POST   /api/v1/properties           - Create property
GET    /api/v1/properties/:id       - Get property details
PUT    /api/v1/properties/:id       - Update property
DELETE /api/v1/properties/:id       - Delete property

# Units
POST   /api/v1/properties/:id/units - Add unit to property
GET    /api/v1/units/:id            - Get unit details
PUT    /api/v1/units/:id            - Update unit

# Tenants & Leases
POST   /api/v1/tenants              - Create tenant
GET    /api/v1/tenants              - List tenants
POST   /api/v1/leases               - Create lease
GET    /api/v1/leases               - List leases

# Payments
POST   /api/v1/payments             - Record payment
GET    /api/v1/leases/:id/payments  - Payment history

# Maintenance
POST   /api/v1/maintenance          - Create request
GET    /api/v1/maintenance          - List requests
PATCH  /api/v1/maintenance/:id      - Update status

# Dashboard
GET    /api/v1/dashboard/summary    - Dashboard data
```

## 🧪 Testing

### Backend Tests
```bash
# Run all tests
go test ./...

# Run with coverage
go test -cover ./...

# Run integration tests (requires test database)
go test ./tests/integration/...
```

### Frontend Tests
```bash
# Run component tests
npm test

# Run with coverage
npm run test:coverage

# Run E2E tests (if configured)
npm run test:e2e
```

## 🚢 Deployment

### Production Build
```bash
# Build backend
cd backend && go build -o bin/server cmd/server/main.go

# Build frontend
cd frontend && npm run build
```

### Docker Production
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Setup
- Set strong JWT secrets
- Configure secure database credentials
- Set up S3 bucket for file storage
- Enable HTTPS/SSL certificates
- Configure rate limiting and monitoring

## 📋 MVP Acceptance Criteria

- [ ] Landlord can register and login securely
- [ ] Landlord can create properties with multiple units
- [ ] Landlord can add tenants and create leases
- [ ] Landlord can record rent payments and view history
- [ ] Anyone can create maintenance requests
- [ ] Landlord can update maintenance request status
- [ ] Dashboard shows occupancy and overdue payments
- [ ] All data is scoped to the authenticated user

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Go best practices and gofmt
- Use TypeScript strictly in frontend
- Write tests for new features
- Update documentation as needed
- Use conventional commit messages

## 🐛 Troubleshooting

### Common Issues

**Database connection errors:**
```bash
# Check if database is running
docker-compose ps

# View database logs
docker-compose logs db

# Reset database
docker-compose down -v && docker-compose up db
```

**Frontend not connecting to API:**
- Check `NEXT_PUBLIC_API_URL` in `.env.local`
- Verify backend is running on correct port
- Check browser network tab for CORS errors

**Authentication issues:**
- Verify JWT_SECRET is set in backend
- Clear browser cookies and localStorage
- Check token expiration times

**Migration errors:**
```bash
# Check migration status
migrate -path backend/internal/db/migrations -database $DATABASE_URL version

# Force version (careful!)
migrate -path backend/internal/db/migrations -database $DATABASE_URL force <version>
```

## 📚 Additional Resources

- [Go Project Layout](https://github.com/golang-standards/project-layout)
- [Next.js Documentation](https://nextjs.org/docs)
- [PostgreSQL Best Practices](https://wiki.postgresql.org/wiki/Don%27t_Do_This)
- [sqlc Documentation](https://docs.sqlc.dev/)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Ready to build?** Start with `docker-compose up` and visit http://localhost:3000