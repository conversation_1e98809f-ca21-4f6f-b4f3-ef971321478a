'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/stores/authStore';
import { dashboardApi } from '@/lib/api';
import { DashboardStats } from '@/types';
import { Building, Home, Users, DollarSign } from 'lucide-react';

export default function DashboardPage() {
  const { user } = useAuthStore();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      const token = localStorage.getItem('access_token');
      if (!token) return;

      try {
        const response = await dashboardApi.getStats(token);
        if (response.success && response.data) {
          setStats(response.data);
        }
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return <div>Loading dashboard...</div>;
  }

  const statsCards = [
    {
      name: 'Total Properties',
      value: stats?.total_properties || 0,
      icon: Building,
      color: 'bg-blue-500',
    },
    {
      name: 'Total Units',
      value: stats?.total_units || 0,
      icon: Home,
      color: 'bg-green-500',
    },
    {
      name: 'Occupied Units',
      value: stats?.occupied_units || 0,
      icon: Users,
      color: 'bg-purple-500',
    },
    {
      name: 'Monthly Rent',
      value: `KSh ${(stats?.total_monthly_rent || 0).toLocaleString()}`,
      icon: DollarSign,
      color: 'bg-yellow-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome back, {user?.name}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statsCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`${stat.color} p-3 rounded-md`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Stats */}
      {stats && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg font-medium text-gray-900">Occupancy Rate</h3>
              <div className="mt-3">
                <div className="text-3xl font-bold text-green-600">
                  {stats.total_units > 0 
                    ? Math.round((stats.occupied_units / stats.total_units) * 100)
                    : 0}%
                </div>
                <p className="text-sm text-gray-500">
                  {stats.occupied_units} of {stats.total_units} units occupied
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg font-medium text-gray-900">Active Leases</h3>
              <div className="mt-3">
                <div className="text-3xl font-bold text-blue-600">
                  {stats.active_leases}
                </div>
                <p className="text-sm text-gray-500">Currently active</p>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <h3 className="text-lg font-medium text-gray-900">Vacant Units</h3>
              <div className="mt-3">
                <div className="text-3xl font-bold text-yellow-600">
                  {stats.vacant_units}
                </div>
                <p className="text-sm text-gray-500">Available for rent</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}