'use client';

import { BarChart3 } from 'lucide-react';

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
        <p className="text-gray-600">View property performance and insights</p>
      </div>

      <div className="text-center py-12">
        <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Analytics Dashboard</h3>
        <p className="mt-1 text-sm text-gray-500">Coming soon - view detailed analytics and reports.</p>
      </div>
    </div>
  );
}