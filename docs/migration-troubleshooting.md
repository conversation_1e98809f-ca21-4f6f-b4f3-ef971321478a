# Database Migration Troubleshooting Guide

## Current Status ✅

As of the latest investigation, **all migrations are working correctly**:
- ✅ All 12 migrations applied successfully
- ✅ All tables created (users, properties, units, leases, payments, etc.)
- ✅ All views created (unit_occupancy, lease_payment_summary, property_maintenance_summary)
- ✅ All functions working (calculate_rent_due, get_property_dashboard_stats)
- ✅ Application running without errors

## Common Migration Issues & Solutions

### 1. Database Connection Errors

**Symptoms:**
```
Failed to connect to database: failed to ping database: dial tcp connect: connection refused
```

**Solutions:**
```bash
# Check if database container is running
docker-compose ps db

# Restart database if needed
docker-compose restart db

# Wait for database to be ready
sleep 10

# Restart backend
docker-compose restart backend
```

### 2. Migration Lock/Dirty State

**Symptoms:**
```
migration failed: Dirty database version X. Fix and force version.
```

**Solutions:**
```bash
# Check migration status
docker-compose exec db psql -U postgres -d rental_mvp -c "SELECT version, dirty FROM schema_migrations;"

# If dirty=true, force clean state (CAREFUL!)
docker-compose exec db psql -U postgres -d rental_mvp -c "UPDATE schema_migrations SET dirty = false;"

# Restart backend to retry migrations
docker-compose restart backend
```

### 3. Duplicate Migration Attempts

**Symptoms:**
- Multiple "Database migrations completed successfully" messages
- Concurrent migration errors

**Solutions:**
```bash
# Stop all containers
docker-compose down

# Start database first
docker-compose up -d db

# Wait for database to be ready
sleep 10

# Start backend
docker-compose up -d backend
```

### 4. Missing Migration Files

**Symptoms:**
```
failed to create migrate instance: file does not exist
```

**Solutions:**
```bash
# Check if migration files exist
ls -la backend/internal/db/migrations/

# Verify migrations path in config
grep MIGRATIONS_PATH backend/.env.development
```

## Manual Migration Commands

### Check Migration Status
```bash
# View current migration version
docker-compose exec db psql -U postgres -d rental_mvp -c "SELECT * FROM schema_migrations;"

# List all tables
docker-compose exec db psql -U postgres -d rental_mvp -c "\dt"

# List all views
docker-compose exec db psql -U postgres -d rental_mvp -c "\dv"

# List all functions
docker-compose exec db psql -U postgres -d rental_mvp -c "\df"
```

### Run Migrations
```bash
# Use the migration script
./scripts/migrate-up.sh

# Or restart backend (auto-migration enabled)
docker-compose restart backend
```

### Reset Database (Nuclear Option)
```bash
# ⚠️ WARNING: This will delete ALL data!
docker-compose down -v
docker-compose up -d
```

## Debugging Steps

### 1. Check Logs
```bash
# Backend logs
docker-compose logs backend --tail=50

# Database logs
docker-compose logs db --tail=50

# All logs
docker-compose logs --tail=50
```

### 2. Test Database Connection
```bash
# Connect to database
docker-compose exec db psql -U postgres -d rental_mvp

# Test basic query
docker-compose exec db psql -U postgres -d rental_mvp -c "SELECT 1;"
```

### 3. Verify Configuration
```bash
# Check environment variables
docker-compose exec backend env | grep DATABASE

# Check config loading
docker-compose exec backend cat /app/.env.development
```

## Prevention Tips

1. **Always backup before migrations in production**
2. **Test migrations in development first**
3. **Monitor logs during deployment**
4. **Use proper migration versioning**
5. **Avoid manual database changes**

## Emergency Recovery

If migrations are completely broken:

1. **Backup current data** (if any important data exists)
2. **Reset database**: `docker-compose down -v`
3. **Start fresh**: `docker-compose up -d`
4. **Restore data** from backup if needed

## Contact & Support

If you continue experiencing migration issues:
1. Check the logs first: `docker-compose logs backend`
2. Verify database connectivity: `docker-compose exec db psql -U postgres -d rental_mvp -c "SELECT 1;"`
3. Try the troubleshooting steps above
4. Create an issue with full error logs if problems persist
